<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
/** @noinspection HtmlDeprecatedAttribute */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_AufzugdokumenteRueckgabeprotokoll())->getData($_GET['schemaId'], $_GET['documentId']);
}
function renderMultiCheckbox(array|string, mixed| $data, string $key, array|string| $values): void
{
    foreach ($values as $value) {
        $checked = in_array($value, $data[$key] ?? []);
        $classes = 'checkbox-size' . ($checked ? '' : ' unchecked');
        $icon = $checked ? '&#10003;' : '&#9744;';
        echo "<span class=\"$classes\">$icon</span> $value";
    }
}

?>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Aufzugdokumente Rückgabeprotokoll</title>
</head>
<body>
<div class="page">
    <table class="first-header-table">
        <tr>
            <td colspan="1">
                <?php renderMultiCheckbox($data['schema'], 'Rückgabeprotokoll Zahnstangenaufzüge Übergabeprotokoll Zahnstangenaufzüge', ['Aufbau']); ?>
            </td>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td colspan="1">
                <?php renderMultiCheckbox($data['schema'], 'Rückgabeprotokoll Zahnstangenaufzüge Übergabeprotokoll Zahnstangenaufzüge', ['Monatliche Kontrolle']); ?>
            </td>
            <td colspan="3"><u><strong style="font-size: 20px">Rückgabeprotokoll Zahnstangenaufzüge</strong></u></td>
        </tr>
        <tr>
            <td colspan="1">
                <?php renderMultiCheckbox($data['schema'], 'Rückgabeprotokoll Zahnstangenaufzüge Übergabeprotokoll Zahnstangenaufzüge', ['Abbau']); ?>
            </td>
            <td colspan="3"></td>
        </tr>
    </table>
    <table class="header-info-table">
        <tr>
            <td colspan="4">Übernommen durch:</td>
        </tr>
        <tr>
            <td colspan="1">Firma:</td>
            <td colspan="3"><u><?= $data['schema']['Übernommen durch Firma'] ?? '' ?></u></td>
        </tr>
        <tr>
            <td colspan="1">Herr/Frau:</td>
            <td colspan="3"><u><?= $data['schema']['Übernommen durch Herr/Frau'] ?? '' ?></u></td>
        </tr>
        <tr>
            <td colspan="1">Anschrift:</td>
            <td colspan="3"><u><?= $data['schema']['Übernommen durch Anschrift'] ?? '' ?></u></td>
        </tr>
        <tr>
            <td colspan="1">Baustelle:</td>
            <td colspan="3"><u><?= $data['schema']['Übernommen durch Baustelle'] ?? '' ?></u></td>
        </tr>
        <tr>
            <td colspan="1" style="min-width: 80px">Fabrik-Nr.:</td>
            <td colspan="1"><u><?= $data['schema']['Übernommen durch Fabrik-Nr.'] ?? '' ?></u></td>
            <td colspan="1"><strong>Typ: Transportbühne GEDA 500/1200/1500 ZP/ZZP</strong></td>
            <td colspan="1">
                <u><?= $data['schema']['Aufzugdokumente Rückgabeprotokoll Typ: Transportbühne GEDA 500/1200/1500 ZP/ZZP'] ?? '' ?></u>
            </td>
        </tr>
        <tr>
            <td colspan="1" style="min-width: 80px">Fabrik-Nr.:</td>
            <td colspan="1"><u><?= $data['schema']['Übernommen durch Fabrik-Nr.2'] ?? '' ?></u></td>
            <td colspan="1"><strong>Typ: Transportbühne ALIMAK</strong></td>
            <td colspan="1">
                <u><?= $data['schema']['Aufzugdokumente Rückgabeprotokoll Typ: Transportbühne ALIMAK'] ?? '' ?></u></td>
        </tr>
    </table>
    <div class="main-table-text">
        <p>
            <strong>Transportbühne</strong> wurde gemäß Stücklisten-Nr.:
            <u><?= $data['schema']['Rückgabeprotokoll Zahnstangenaufzüge Transportbühne wurde gemäß Stücklisten-Nr.:'] ?? '' ?></u>
            übergeben.
        </p>
    </div>
    <table class="main-table">
        <tr>
            <td colspan="2"><strong>Prüfprotokoll</strong></td>
            <td colspan="1"><strong>Befund</strong></td>
            <td colspan="1"><strong>Bemerkungen</strong></td>
        </tr>
        <tr>
            <td colspan="2">
                <strong>Sichtprüfung Bühne:</strong><br>
                (Steht Bühne schief? Etwas eingeklemmt? Mast gerade?)
            </td>
            <td colspan="1"><?= $data['schema']['Sichtprüfung Bühne Befund'] ?? '' ?></td>
            <td colspan="1"><?= $data['schema']['Sichtprüfung Bühne Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">
                <strong>Sichtprüfung Kabeltopf:</strong><br>
                (Auf Dellen und Schmutz prüfen)
            </td>
            <td colspan="1"><?= $data['schema']['Sichtprüfung Kabeltopf Befund'] ?? '' ?></td>
            <td colspan="1"><?= $data['schema']['Sichtprüfung Kabeltopf Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">
                <strong>Sichtprüfung Zahnstange:</strong> <br>
                (abgenutzt oder defekt? Schmierung?)
            </td>
            <td colspan="1"><?= $data['schema']['Sichtprüfung Zahnstange Befund'] ?? '' ?></td>
            <td colspan="1"><?= $data['schema']['Sichtprüfung Zahnstange Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">
                <strong>Sichtprüfung Kabel/Elektrik:</strong><br>
                (Kabel, Stecker, Verschraubung?)
            </td>
            <td colspan="1"><?= $data['schema']['Sichtprüfung Kabel/Elektrik Befund'] ?? '' ?></td>
            <td colspan="1"><?= $data['schema']['Sichtprüfung Kabel/Elektrik Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">
                <strong>Sichtprüfung Bedienung:</strong><br>
                (Fernbedienung, Anschlusskabel, Blindstecker vorhanden?)
            </td>
            <td colspan="1"><?= $data['schema']['Sichtprüfung Bedienung Befund'] ?? '' ?></td>
            <td colspan="1"><?= $data['schema']['Sichtprüfung Bedienung Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">
                <strong>Funktionstest Bühne:</strong><br>
                (Bühne hochfahren, Unterfahrschutz usw.)
            </td>
            <td colspan="1"><?= $data['schema']['Funktionstest Bühne Befund'] ?? '' ?></td>
            <td colspan="1"><?= $data['schema']['Funktionstest Bühne Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">
                <strong>Funktionstest Etagentore:</strong><br>
                (Abstand zur Bühne, sind alle Tore korrekt eingestellt?)
            </td>
            <td colspan="1"><?= $data['schema']['Funktionstest Etagentore Befund'] ?? '' ?></td>
            <td colspan="1"><?= $data['schema']['Funktionstest Etagentore Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">
                <strong>Funktionstest Hupe:</strong><br>
                (Hupe funktioniert?)
            </td>
            <td colspan="1"><?= $data['schema']['Funktionstest Hupe Befund'] ?? '' ?></td>
            <td colspan="1"><?= $data['schema']['Funktionstest Hupe Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">
                <strong>Anleitung und Warnhinweise:</strong> <br>
                (Bedienungsanleitung und alle Warnhinweise vorhanden?)
            </td>
            <td colspan="1"><?= $data['schema']['Anleitung und Warnhinweise Befund'] ?? '' ?></td>
            <td colspan="1"><?= $data['schema']['Anleitung und Warnhinweise Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">
                <strong>Prüfung Mastschrauben:</strong><br>
                (Mastschrauben prüfen, 150Nm)
            </td>
            <td colspan="1"><?= $data['schema']['Prüfung Mastschrauben Befund'] ?? '' ?></td>
            <td colspan="1"><?= $data['schema']['Prüfung Mastschrauben Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2"></td>
            <td colspan="1"></td>
            <td colspan="1"></td>
        </tr>
    </table>
    <table class="last-table" style="margin-top: 30px">
        <tr>
            <td colspan="1">
                <strong>Sonstiges:</strong>
            </td>
            <td colspan="3">
                <u><?= $data['schema']['Rückgabeprotokoll Zahnstangenaufzüge Sonstiges'] ?? '' ?></u>
            </td>
        </tr>
        <tr>
            <td colspan="1" style="width: 35%">
                <strong>Bilderreihe Aufbau/Abbau:</strong>
            </td>
            <td colspan="3">
                <p>
                    Aufzugsnummer, TÜV + Typenschild, Bühne von außen, Kabeltopf Bühne
                    innen, Mast, Etagentore, Fettpumpe, Aufstellung
                </p>
            </td>
        </tr>
        <tr>
            <td>
                <div style="height: 30px"></div>
            </td>
        </tr>
        <tr>
            <td colspan="2" class="last-section">
                <p>Ort, Datum:
                    <u>
                        <?php
                        $ortRaw = $data['schema']['Rückgabeprotokoll Zahnstangenaufzüge Ort'] ?? '';
                        $datumRaw = $data['schema']['Rückgabeprotokoll Zahnstangenaufzüge Datum'] ?? '';
                        $datum = $datumRaw ? date('d.m.Y', strtotime($datumRaw)) : '';
                        $parts = array_filter([$ortRaw, $datum]);
                        echo '<u>' . implode(', ', $parts) . '</u>';
                        ?>
                    </u>
                </p>
            </td>
            <td colspan="2" class="last-section" style="text-align: right">
                <p>Unterschrift:
                    <u>
                        <?php if (isset($data['schema']['Rückgabeprotokoll Zahnstangenaufzüge Unterschrift'])) { ?>
                            <img src="<?= $data['schema']['Rückgabeprotokoll Zahnstangenaufzüge Unterschrift']; ?>"
                                 alt="signature" class="signature">
                        <?php } ?>
                    </u>
                </p>
            </td>
    </table>
</div>
</body>
</html>