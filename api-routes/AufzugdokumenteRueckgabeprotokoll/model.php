<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_AufzugdokumenteRueckgabeprotokoll
{
    public function getData(string $schemaId, string $documentId): array|string, mixed|
    {
        $schemaId = (int)$schemaId;
        $documentId = (int)$documentId;
        $curl = new PrintoutCurl();
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $data['schema'] = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument'], true);
        return $data;
    }
}